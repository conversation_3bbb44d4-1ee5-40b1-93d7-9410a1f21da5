/**
 * BERT模型演示脚本
 * 用于验证BERTModel的功能实现
 */
import { BERTModel, BERTModelConfig } from './BERTModel';

async function runBERTDemo() {
  console.log('🚀 开始BERT模型功能演示...\n');

  // 创建模型配置
  const config: BERTModelConfig = {
    variant: 'base',
    maxSequenceLength: 512,
    confidenceThreshold: 0.7,
    keywordCount: 10,
    debug: true
  };

  // 创建模型实例
  const model = new BERTModel(config);

  try {
    // 1. 初始化模型
    console.log('📋 1. 初始化模型...');
    const initResult = await model.initialize();
    console.log('初始化结果:', initResult ? '✅ 成功' : '❌ 失败');
    console.log('模型ID:', model.getId());
    console.log('模型类型:', model.getType());
    console.log();

    // 2. 文本分类
    console.log('📋 2. 文本分类测试...');
    const classificationTexts = [
      '这个产品质量很好，我很满意！',
      '服务态度太差了，非常不满意',
      '这是一个普通的产品，没什么特别的'
    ];

    for (const text of classificationTexts) {
      const classificationResult = await model.classifyText(text);
      console.log(`文本: "${text}"`);
      console.log(`分类: ${classificationResult.label} (置信度: ${classificationResult.confidence.toFixed(2)})`);
      console.log();
    }

    // 3. 自定义类别分类
    console.log('📋 3. 自定义类别分类测试...');
    const categories = ['科技', '娱乐', '体育', '政治'];
    const techText = '人工智能技术发展迅速，深度学习算法不断进步';
    const customClassificationResult = await model.classifyText(techText, categories);
    console.log('输入文本:', techText);
    console.log('自定义类别:', categories);
    console.log('分类结果:', customClassificationResult);
    console.log();

    // 4. 命名实体识别
    console.log('📋 4. 命名实体识别测试...');
    const entityText = '张三在北京的清华大学工作，他的电话是13800138000';
    const entityResult = await model.recognizeEntities(entityText);
    console.log('输入文本:', entityText);
    console.log('识别的实体:');
    entityResult.entities.forEach((entity, index) => {
      console.log(`  ${index + 1}. ${entity.text} (${entity.type}) - 置信度: ${entity.confidence.toFixed(2)}`);
    });
    console.log();

    // 5. 文本摘要
    console.log('📋 5. 文本摘要测试...');
    const longText = `
      人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，
      并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、
      语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，
      应用领域也不断扩大，可以设想，未来人工智能带来的科技产品，将会是人类智慧的"容器"。
      BERT模型是Google开发的一种双向编码器表示，特别适合文本理解和分析任务。
    `.trim();
    
    const summaryResult = await model.summarizeText(longText, 80);
    console.log('原文长度:', longText.length);
    console.log('摘要:', summaryResult.summary);
    console.log('摘要长度:', summaryResult.length);
    console.log('压缩率:', (summaryResult.compressionRate * 100).toFixed(1) + '%');
    console.log();

    // 6. 情感分析
    console.log('📋 6. 情感分析测试...');
    const emotionTexts = [
      '我今天心情很好！',
      '这让我感到很愤怒',
      '我对此感到惊讶',
      '这是一个普通的日子'
    ];

    for (const text of emotionTexts) {
      const emotionResult = await model.analyzeEmotion(text);
      console.log(`文本: "${text}"`);
      console.log(`主要情感: ${emotionResult.primaryEmotion} (强度: ${emotionResult.intensity.toFixed(2)})`);
      console.log();
    }

    // 7. 关键词提取
    console.log('📋 7. 关键词提取测试...');
    const keywordText = '人工智能技术在医疗健康领域的应用越来越广泛，包括疾病诊断、药物研发、个性化治疗等方面';
    const keywordResult = await model.extractKeywords(keywordText, { count: 5 });
    console.log('输入文本:', keywordText);
    console.log('提取的关键词:', keywordResult.keywords);
    console.log('重要性分数:', keywordResult.scores.map(s => s.toFixed(3)));
    console.log();

    // 8. 文本相似度计算
    console.log('📋 8. 文本相似度计算测试...');
    const text1 = '人工智能技术发展迅速';
    const text2 = 'AI技术进步很快';
    const text3 = '今天天气很好';
    
    const similarity1 = await model.calculateSimilarity(text1, text2);
    const similarity2 = await model.calculateSimilarity(text1, text3);
    
    console.log(`"${text1}" 与 "${text2}" 的相似度: ${similarity1.similarity.toFixed(3)}`);
    console.log(`"${text1}" 与 "${text3}" 的相似度: ${similarity2.similarity.toFixed(3)}`);
    console.log();

    // 9. 语言检测
    console.log('📋 9. 语言检测测试...');
    const languageTexts = [
      '这是一段中文文本',
      'This is an English text',
      'Ceci est un texte français',
      'Dies ist ein deutscher Text'
    ];

    for (const text of languageTexts) {
      const languageResult = await model.detectLanguage(text);
      console.log(`"${text}" -> 语言: ${languageResult.language} (置信度: ${languageResult.confidence.toFixed(2)})`);
    }
    console.log();

    // 10. 文本纠错
    console.log('📋 10. 文本纠错测试...');
    const errorTexts = [
      'teh quick brown fox',
      '你好吗',
      'recieve the message',
      '谢谢你'
    ];

    for (const text of errorTexts) {
      const correctionResult = await model.correctText(text);
      if (correctionResult.corrections.length > 0) {
        console.log(`原文: "${text}"`);
        console.log(`纠错: "${correctionResult.correctedText}"`);
        console.log(`错误数: ${correctionResult.statistics?.totalErrors || 0}`);
      } else {
        console.log(`"${text}" -> 无需纠错`);
      }
    }
    console.log();

    // 11. 问答系统
    console.log('📋 11. 问答系统测试...');
    const questions = [
      { question: '什么是人工智能？', context: '人工智能是计算机科学的一个分支' },
      { question: '如何学习机器学习？', context: undefined },
      { question: '为什么BERT模型适合文本理解？', context: 'BERT是双向编码器表示模型' }
    ];

    for (const item of questions) {
      const qaResult = await model.answerQuestion(item.question, { context: item.context });
      console.log(`问题: "${item.question}"`);
      console.log(`回答: "${qaResult.answer}"`);
      console.log(`置信度: ${qaResult.confidence.toFixed(2)}`);
      console.log();
    }

    // 12. 意图识别
    console.log('📋 12. 意图识别测试...');
    const intentTexts = [
      '我想预订一张机票',
      '帮我查询天气',
      '取消我的订单',
      '你好',
      '再见'
    ];

    for (const text of intentTexts) {
      const intentResult = await model.recognizeIntent(text);
      console.log(`文本: "${text}"`);
      console.log(`意图: ${intentResult.intent} (置信度: ${intentResult.confidence.toFixed(2)})`);
      console.log(`实体:`, intentResult.entities);
      console.log();
    }

    // 13. 错误处理测试
    console.log('📋 13. 错误处理测试...');
    try {
      await model.generateText('测试提示');
    } catch (error) {
      console.log('✅ 正确捕获错误:', (error as Error).message);
    }
    console.log();

    console.log('🎉 BERT模型功能演示完成！');

  } catch (error) {
    console.error('❌ 演示过程中发生错误:', error);
  } finally {
    // 清理资源
    model.dispose();
    console.log('🧹 资源已清理');
  }
}

// 运行演示
if (require.main === module) {
  runBERTDemo().catch(console.error);
}

export { runBERTDemo };
