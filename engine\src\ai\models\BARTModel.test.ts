/**
 * BART模型测试
 */
import { BARTModel, BARTModelConfig } from './BARTModel';
import { AIModelType } from '../AIModelType';

describe('BARTModel', () => {
  let model: BARTModel;
  let config: BARTModelConfig;

  beforeEach(() => {
    config = {
      debug: true,
      variant: 'large',
      maxLength: 512,
      minLength: 10,
      useBeamSearch: true,
      beamSize: 4
    };
    model = new BARTModel(config);
  });

  afterEach(() => {
    model.dispose();
  });

  describe('基本功能', () => {
    test('应该正确获取模型ID', () => {
      expect(model.getId()).toBe('bart-large');
    });

    test('应该正确获取模型类型', () => {
      expect(model.getType()).toBe(AIModelType.BART);
    });

    test('应该正确获取模型配置', () => {
      const modelConfig = model.getConfig();
      expect(modelConfig.variant).toBe('large');
      expect(modelConfig.maxLength).toBe(512);
    });

    test('应该能够初始化模型', async () => {
      const result = await model.initialize();
      expect(result).toBe(true);
    });
  });

  describe('文本生成功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够生成文本', async () => {
      const result = await model.generateText('请生成一段关于人工智能的文本');
      
      expect(result).toHaveProperty('text');
      expect(result).toHaveProperty('tokens');
      expect(typeof result.text).toBe('string');
      expect(result.text.length).toBeGreaterThan(0);
    });

    test('应该能够使用选项生成文本', async () => {
      const options = {
        maxLength: 100,
        temperature: 0.8,
        topP: 0.9
      };
      
      const result = await model.generateText('生成文本', options);
      expect(result.text).toBeDefined();
    });
  });

  describe('文本摘要功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够生成摘要', async () => {
      const longText = '人工智能是计算机科学的一个分支。它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。';
      const result = await model.summarizeText(longText, 50);
      
      expect(result).toHaveProperty('summary');
      expect(result).toHaveProperty('length');
      expect(result).toHaveProperty('compressionRate');
      expect(result.summary.length).toBeLessThanOrEqual(50);
    });
  });

  describe('翻译功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够翻译文本', async () => {
      const result = await model.translateText('Hello world', 'zh');
      
      expect(result).toHaveProperty('translatedText');
      expect(result).toHaveProperty('sourceLanguage');
      expect(result).toHaveProperty('targetLanguage');
      expect(result).toHaveProperty('confidence');
      expect(result.targetLanguage).toBe('zh');
    });

    test('应该能够指定源语言翻译', async () => {
      const result = await model.translateText('Hello world', 'zh', 'en');
      
      expect(result.sourceLanguage).toBe('en');
      expect(result.targetLanguage).toBe('zh');
    });
  });

  describe('文本分类功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够分类文本', async () => {
      const result = await model.classifyText('这是一个很好的产品');
      
      expect(result).toHaveProperty('label');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('allLabels');
    });

    test('应该能够使用自定义类别分类', async () => {
      const categories = ['科技', '娱乐', '体育'];
      const result = await model.classifyText('人工智能技术发展迅速', categories);
      
      expect(categories).toContain(result.label);
    });
  });

  describe('情感分析功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够分析情感', async () => {
      const result = await model.analyzeEmotion('我今天很开心！');
      
      expect(result).toHaveProperty('primaryEmotion');
      expect(result).toHaveProperty('intensity');
      expect(result).toHaveProperty('scores');
      expect(result).toHaveProperty('confidence');
    });
  });

  describe('命名实体识别功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够识别实体', async () => {
      const result = await model.recognizeEntities('张三在北京工作');
      
      expect(result).toHaveProperty('entities');
      expect(Array.isArray(result.entities)).toBe(true);
    });
  });

  describe('问答功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够回答问题', async () => {
      const result = await model.answerQuestion('什么是人工智能？');
      
      expect(result).toHaveProperty('answer');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('sources');
    });

    test('应该能够基于上下文回答问题', async () => {
      const context = '人工智能是计算机科学的一个分支';
      const result = await model.answerQuestion('什么是AI？', { context });
      
      expect(result.answer).toBeDefined();
      expect(result.sources).toBeDefined();
    });
  });

  describe('对话处理功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够处理对话', async () => {
      const result = await model.processDialogue('你好', 'session1', 'user1');
      
      expect(result).toHaveProperty('response');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('intent');
      expect(result).toHaveProperty('context');
    });
  });

  describe('语言检测功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够检测中文', async () => {
      const result = await model.detectLanguage('这是一段中文文本');
      
      expect(result).toHaveProperty('language');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('allLanguages');
      expect(result.language).toBe('zh');
    });

    test('应该能够检测英文', async () => {
      const result = await model.detectLanguage('This is an English text');
      
      expect(result.language).toBe('en');
    });
  });

  describe('文本纠错功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够纠错文本', async () => {
      const result = await model.correctText('teh quick brown fox');
      
      expect(result).toHaveProperty('correctedText');
      expect(result).toHaveProperty('corrections');
      expect(result).toHaveProperty('statistics');
    });
  });

  describe('事件监听', () => {
    test('应该能够监听初始化事件', (done) => {
      model.on('initialized', (data) => {
        expect(data.success).toBe(true);
        done();
      });
      
      model.initialize();
    });
  });
});
