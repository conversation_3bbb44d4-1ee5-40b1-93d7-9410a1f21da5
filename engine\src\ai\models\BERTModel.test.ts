/**
 * BERT模型测试
 */
import { BERTModel, BERTModelConfig } from './BERTModel';
import { AIModelType } from '../AIModelType';

describe('BERTModel', () => {
  let model: BERTModel;
  let config: BERTModelConfig;

  beforeEach(() => {
    config = {
      debug: true,
      variant: 'base',
      maxSequenceLength: 512,
      confidenceThreshold: 0.7,
      keywordCount: 10
    };
    model = new BERTModel(config);
  });

  afterEach(() => {
    model.dispose();
  });

  describe('基本功能', () => {
    test('应该正确获取模型ID', () => {
      expect(model.getId()).toBe('bert-undefined');
    });

    test('应该正确获取模型类型', () => {
      expect(model.getType()).toBe(AIModelType.BERT);
    });

    test('应该正确获取模型配置', () => {
      const modelConfig = model.getConfig();
      expect(modelConfig.variant).toBe('base');
      expect(modelConfig.maxSequenceLength).toBe(512);
    });

    test('应该能够初始化模型', async () => {
      const result = await model.initialize();
      expect(result).toBe(true);
    });
  });

  describe('文本分类功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够分类文本', async () => {
      const result = await model.classifyText('这是一个很好的产品');
      
      expect(result).toHaveProperty('label');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('allLabels');
      expect(typeof result.confidence).toBe('number');
      expect(result.confidence).toBeGreaterThan(0);
      expect(result.confidence).toBeLessThanOrEqual(1);
    });

    test('应该能够使用自定义类别分类', async () => {
      const categories = ['技术', '商业', '娱乐'];
      const result = await model.classifyText('人工智能技术发展迅速', categories);
      
      expect(categories).toContain(result.label);
      expect(Object.keys(result.allLabels)).toEqual(expect.arrayContaining(categories));
    });
  });

  describe('命名实体识别功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够识别实体', async () => {
      const result = await model.recognizeEntities('张三在北京工作');
      
      expect(result).toHaveProperty('entities');
      expect(Array.isArray(result.entities)).toBe(true);
      
      if (result.entities.length > 0) {
        const entity = result.entities[0];
        expect(entity).toHaveProperty('text');
        expect(entity).toHaveProperty('type');
        expect(entity).toHaveProperty('start');
        expect(entity).toHaveProperty('end');
        expect(entity).toHaveProperty('confidence');
      }
    });
  });

  describe('文本摘要功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够生成摘要', async () => {
      const longText = '人工智能是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。';
      const result = await model.summarizeText(longText, 50);
      
      expect(result).toHaveProperty('summary');
      expect(result).toHaveProperty('length');
      expect(result).toHaveProperty('compressionRate');
      expect(result.summary.length).toBeLessThanOrEqual(50);
      expect(result.length).toBe(result.summary.length);
    });
  });

  describe('情感分析功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够分析情感', async () => {
      const result = await model.analyzeEmotion('我今天很开心！');
      
      expect(result).toHaveProperty('primaryEmotion');
      expect(result).toHaveProperty('intensity');
      expect(result).toHaveProperty('scores');
      expect(result).toHaveProperty('confidence');
      expect(typeof result.intensity).toBe('number');
      expect(result.intensity).toBeGreaterThan(0);
      expect(result.intensity).toBeLessThanOrEqual(1);
    });
  });

  describe('关键词提取功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够提取关键词', async () => {
      const text = '人工智能技术在医疗健康领域的应用越来越广泛，包括疾病诊断、药物研发、个性化治疗等方面。';
      const result = await model.extractKeywords(text, { count: 5 });
      
      expect(result).toHaveProperty('keywords');
      expect(result).toHaveProperty('scores');
      expect(result).toHaveProperty('details');
      expect(Array.isArray(result.keywords)).toBe(true);
      expect(result.keywords.length).toBeLessThanOrEqual(5);
    });
  });

  describe('文本相似度计算功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够计算文本相似度', async () => {
      const text1 = '人工智能技术发展迅速';
      const text2 = 'AI技术进步很快';
      const result = await model.calculateSimilarity(text1, text2);
      
      expect(result).toHaveProperty('similarity');
      expect(result).toHaveProperty('method');
      expect(result).toHaveProperty('details');
      expect(typeof result.similarity).toBe('number');
      expect(result.similarity).toBeGreaterThanOrEqual(0);
      expect(result.similarity).toBeLessThanOrEqual(1);
    });
  });

  describe('语言检测功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够检测中文', async () => {
      const result = await model.detectLanguage('这是一段中文文本');
      
      expect(result).toHaveProperty('language');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('allLanguages');
      expect(result.language).toBe('zh');
    });

    test('应该能够检测英文', async () => {
      const result = await model.detectLanguage('This is an English text');
      
      expect(result.language).toBe('en');
    });
  });

  describe('文本纠错功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够纠错文本', async () => {
      const result = await model.correctText('teh quick brown fox');
      
      expect(result).toHaveProperty('correctedText');
      expect(result).toHaveProperty('corrections');
      expect(result).toHaveProperty('statistics');
      expect(Array.isArray(result.corrections)).toBe(true);
    });
  });

  describe('问答功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够回答问题', async () => {
      const result = await model.answerQuestion('什么是人工智能？');
      
      expect(result).toHaveProperty('answer');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('sources');
      expect(typeof result.answer).toBe('string');
      expect(result.answer.length).toBeGreaterThan(0);
    });

    test('应该能够基于上下文回答问题', async () => {
      const context = '人工智能是计算机科学的一个分支';
      const result = await model.answerQuestion('什么是AI？', { context });
      
      expect(result.answer).toBeDefined();
      expect(result.sources).toBeDefined();
    });
  });

  describe('意图识别功能', () => {
    beforeEach(async () => {
      await model.initialize();
    });

    test('应该能够识别意图', async () => {
      const result = await model.recognizeIntent('我想预订一张机票');
      
      expect(result).toHaveProperty('intent');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('entities');
      expect(result).toHaveProperty('parameters');
      expect(result.intent).toBe('booking');
    });

    test('应该能够识别问候意图', async () => {
      const result = await model.recognizeIntent('你好');
      
      expect(result.intent).toBe('greeting');
      expect(result.confidence).toBeGreaterThan(0.8);
    });
  });

  describe('错误处理', () => {
    test('生成文本应该抛出错误', async () => {
      await expect(model.generateText('test prompt')).rejects.toThrow('BERT模型不支持文本生成');
    });
  });

  describe('事件监听', () => {
    test('应该能够监听初始化事件', (done) => {
      model.on('initialized', (data) => {
        expect(data.success).toBe(true);
        done();
      });
      
      model.initialize();
    });
  });
});
