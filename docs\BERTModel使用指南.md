# BERT模型使用指南

## 概述

BERT（Bidirectional Encoder Representations from Transformers）是Google开发的一种双向编码器表示模型，特别适合文本理解和分析任务。本文档详细介绍了BERTModel类的功能实现和使用方法。

## 功能特性

### 🎯 核心功能
- **文本分类**：支持多类别文本分类和情感分析
- **命名实体识别**：提取文本中的人名、地名、组织等实体
- **文本摘要**：生成文本的简洁摘要
- **情感分析**：识别文本中的情感倾向和强度
- **关键词提取**：提取文本的重要关键词
- **文本相似度计算**：计算两个文本的相似度
- **语言检测**：自动检测文本语言
- **文本纠错**：识别并修正文本错误
- **问答系统**：基于上下文回答问题
- **意图识别**：识别用户输入的意图

### 🔧 技术特性
- **双向编码**：同时考虑上下文信息
- **多语言支持**：支持10种主要语言
- **可配置参数**：支持灵活的模型配置
- **事件驱动**：提供初始化进度和状态事件
- **错误处理**：完善的异常处理机制

## 快速开始

### 基本使用

```typescript
import { BERTModel, BERTModelConfig } from './BERTModel';

// 创建模型配置
const config: BERTModelConfig = {
  variant: 'base',
  maxSequenceLength: 512,
  confidenceThreshold: 0.7,
  keywordCount: 10,
  debug: true
};

// 创建模型实例
const model = new BERTModel(config);

// 初始化模型
await model.initialize();

// 文本分类
const classificationResult = await model.classifyText('这是一个很好的产品');
console.log('分类结果:', classificationResult);

// 命名实体识别
const entityResult = await model.recognizeEntities('张三在北京工作');
console.log('实体识别结果:', entityResult);

// 清理资源
model.dispose();
```

### 高级配置

```typescript
const advancedConfig: BERTModelConfig = {
  variant: 'large',
  maxSequenceLength: 1024,
  emotionCategories: ['positive', 'negative', 'neutral', 'happy', 'sad'],
  supportedLanguages: ['zh', 'en', 'fr', 'de', 'es'],
  entityTypes: ['PERSON', 'ORGANIZATION', 'LOCATION', 'DATE'],
  keywordCount: 15,
  confidenceThreshold: 0.8,
  debug: false
};

const model = new BERTModel(advancedConfig);
```

## 功能详解

### 1. 文本分类

```typescript
// 基本分类
const result = await model.classifyText('这个电影很精彩');

// 自定义类别分类
const categories = ['科技', '娱乐', '体育', '政治'];
const result = await model.classifyText('人工智能技术发展迅速', categories);

console.log('分类标签:', result.label);
console.log('置信度:', result.confidence);
console.log('所有类别分数:', result.allLabels);
```

### 2. 命名实体识别

```typescript
const entityResult = await model.recognizeEntities('张三在北京的清华大学工作');

entityResult.entities.forEach(entity => {
  console.log(`实体: ${entity.text}, 类型: ${entity.type}, 置信度: ${entity.confidence}`);
});
```

### 3. 文本摘要

```typescript
const longText = '这是一段很长的文本内容...';
const summaryResult = await model.summarizeText(longText, 100);

console.log('摘要:', summaryResult.summary);
console.log('摘要长度:', summaryResult.length);
console.log('压缩率:', summaryResult.compressionRate);
```

### 4. 情感分析

```typescript
const emotionResult = await model.analyzeEmotion('我今天心情很好！');

console.log('主要情感:', emotionResult.primaryEmotion);
console.log('情感强度:', emotionResult.intensity);
console.log('情感分数:', emotionResult.scores);
console.log('置信度:', emotionResult.confidence);
```

### 5. 关键词提取

```typescript
const keywordResult = await model.extractKeywords(text, { count: 10 });

console.log('关键词:', keywordResult.keywords);
console.log('重要性分数:', keywordResult.scores);
console.log('详细信息:', keywordResult.details);
```

### 6. 文本相似度计算

```typescript
const text1 = '人工智能技术发展迅速';
const text2 = 'AI技术进步很快';
const similarityResult = await model.calculateSimilarity(text1, text2);

console.log('相似度:', similarityResult.similarity);
console.log('计算方法:', similarityResult.method);
console.log('详细结果:', similarityResult.details);
```

### 7. 语言检测

```typescript
const languageResult = await model.detectLanguage('This is an English text');

console.log('检测语言:', languageResult.language);
console.log('置信度:', languageResult.confidence);
console.log('所有语言分数:', languageResult.allLanguages);
```

### 8. 文本纠错

```typescript
const correctionResult = await model.correctText('teh quick brown fox');

console.log('原文:', 'teh quick brown fox');
console.log('纠错后:', correctionResult.correctedText);
console.log('纠错详情:', correctionResult.corrections);
console.log('错误统计:', correctionResult.statistics);
```

### 9. 问答系统

```typescript
// 基本问答
const result1 = await model.answerQuestion('什么是人工智能？');

// 基于上下文的问答
const context = '人工智能是计算机科学的一个分支...';
const result2 = await model.answerQuestion('AI有什么应用？', { context });

console.log('答案:', result1.answer);
console.log('置信度:', result1.confidence);
console.log('来源:', result1.sources);
```

### 10. 意图识别

```typescript
const intentResult = await model.recognizeIntent('我想预订一张机票');

console.log('识别意图:', intentResult.intent);
console.log('置信度:', intentResult.confidence);
console.log('提取实体:', intentResult.entities);
console.log('参数:', intentResult.parameters);
```

## 配置选项

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| variant | string | 'base' | 模型变体：base/large/multilingual |
| maxSequenceLength | number | 512 | 最大序列长度 |
| emotionCategories | string[] | 默认情感类别 | 自定义情感类别 |
| supportedLanguages | string[] | 默认支持语言 | 支持的语言列表 |
| entityTypes | string[] | 默认实体类型 | 自定义实体类型 |
| keywordCount | number | 10 | 关键词提取数量 |
| confidenceThreshold | number | 0.7 | 置信度阈值 |
| debug | boolean | false | 是否启用调试模式 |

## 事件监听

```typescript
// 监听初始化进度
model.on('loadProgress', (data) => {
  console.log('加载进度:', data.progress * 100 + '%');
});

// 监听初始化完成
model.on('initialized', (data) => {
  if (data.success) {
    console.log('模型初始化成功');
  } else {
    console.error('模型初始化失败:', data.error);
  }
});
```

## 最佳实践

### 1. 资源管理
```typescript
// 始终在使用完毕后清理资源
try {
  const model = new BERTModel(config);
  await model.initialize();
  // 使用模型...
} finally {
  model.dispose();
}
```

### 2. 错误处理
```typescript
try {
  const result = await model.classifyText(text);
  // 处理结果...
} catch (error) {
  console.error('分类失败:', error);
  // 错误处理逻辑...
}
```

### 3. 性能优化
```typescript
// 复用模型实例，避免重复初始化
const model = new BERTModel(config);
await model.initialize();

// 批量处理
const texts = ['文本1', '文本2', '文本3'];
const results = await Promise.all(
  texts.map(text => model.classifyText(text))
);
```

## 应用场景

### 1. 文本分析
- 情感监控
- 内容分类
- 垃圾邮件检测

### 2. 信息提取
- 实体识别
- 关键词提取
- 文档摘要

### 3. 智能客服
- 意图识别
- 问答系统
- 对话理解

### 4. 内容处理
- 文本纠错
- 语言检测
- 相似度匹配

## 注意事项

1. **模型限制**：BERT模型不支持文本生成功能
2. **初始化**：使用任何功能前必须先调用 `initialize()` 方法
3. **资源清理**：使用完毕后应调用 `dispose()` 方法释放资源
4. **异步操作**：所有AI功能都是异步的，需要使用 `await` 或 `.then()`
5. **错误处理**：建议使用 try-catch 包装所有AI操作

## 故障排除

### 常见问题

**Q: 模型初始化失败**
A: 检查配置参数是否正确，确保网络连接正常

**Q: 分类结果不准确**
A: 尝试调整 `confidenceThreshold` 参数或使用更大的模型变体

**Q: 内存使用过高**
A: 减少 `maxSequenceLength` 或使用较小的模型变体

**Q: 处理速度慢**
A: 启用GPU加速或使用较小的模型变体

## 更新日志

### v1.0.0
- ✅ 完整实现所有核心功能
- ✅ 支持多种文本理解任务
- ✅ 添加完善的配置系统
- ✅ 提供详细的错误处理
- ✅ 包含完整的测试用例
